// 现代化商品列表样式
// ================================

// 价格样式现代化
.text-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5c58 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.seckill-price {
  margin-right: 8px;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5722 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

// 现代化商品列表容器
.goods-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
    padding: 0 16px;
  }
}

// 现代化商品卡片
.goods-show-info {
  background: #ffffff;
  border-radius: 16px;
  padding: 0;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
    border-color: rgba(255, 92, 88, 0.2);

    .goods-show-img img {
      transform: scale(1.05);
    }

    .goods-show-detail {
      color: #333;
    }
  }
}
// 商品图片容器
.goods-show-img {
  position: relative;
  width: 100%;
  height: 280px;
  overflow: hidden;
  border-radius: 16px 16px 0 0;
  background: #f8f9fa;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// 商品信息容器
.goods-show-content {
  padding: 20px;
}

// 商品价格样式
.goods-show-price {
  margin: 16px 0 12px 0;
  display: flex;
  align-items: baseline;
  gap: 8px;

  .price-symbol {
    font-size: 16px;
    font-weight: 500;
    color: #666;
  }
}

// 商品标题
.goods-show-detail {
  height: 48px;
  font-size: 15px;
  line-height: 1.6;
  margin: 0 0 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #333;
  font-weight: 500;
  transition: color 0.2s ease;

  &:hover {
    color: #ff5c58;
  }
}

// 评价信息
.goods-show-num {
  font-size: 13px;
  margin-bottom: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;

  span {
    color: #ff5c58;
    font-weight: 600;
  }

  &::before {
    content: "⭐";
    font-size: 12px;
    margin-right: 2px;
  }
}

// 店铺信息
.goods-show-seller {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;

  .text-bottom {
    color: #ff5c58 !important;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}
// 商品标签样式
.goods-show-right {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 12px;

  .goods-show-tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    border: none;

    &[color="red"] {
      background: linear-gradient(135deg, #ff6b6b, #ff5722);
      color: white;
    }

    &[color="blue"] {
      background: linear-gradient(135deg, #4fc3f7, #2196f3);
      color: white;
    }

    &[color="purple"] {
      background: linear-gradient(135deg, #ba68c8, #9c27b0);
      color: white;
    }
  }
}

// 现代化分页样式
.goods-page {
  margin: 40px auto;
  text-align: center;
  max-width: 1400px;
  padding: 0 20px;

  .ivu-page {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;

    .ivu-page-item {
      border-radius: 8px;
      border: 1px solid #e0e0e0;
      transition: all 0.2s ease;

      &:hover {
        border-color: #ff5c58;
        color: #ff5c58;
      }

      &.ivu-page-item-active {
        background: linear-gradient(135deg, #ff6b6b, #ff5c58);
        border-color: #ff5c58;
        color: white;
      }
    }
  }
}